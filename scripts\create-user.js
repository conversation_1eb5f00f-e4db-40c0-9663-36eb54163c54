/**
 * User Creation Script
 *
 * This script creates users of different types (admin, regular user with agent role, regular user)
 * for testing and development purposes.
 *
 * Usage:
 * node scripts/create-user.js --type=admin --email=<EMAIL> --password=Admin@123 --name="Admin User"
 * node scripts/create-user.js --type=agent --email=<EMAIL> --password=Agent@123 --name="Agent User"
 * node scripts/create-user.js --type=user --email=<EMAIL> --password=User@123 --name="Regular User"
 *
 * To create default users of all types:
 * node scripts/create-user.js --type=all
 *
 * Note: The system uses separate JWT tokens for different user types with different secret keys:
 * - Admin users are stored in the 'admin' table
 * - Agent users are regular users with special JWT tokens
 * - Regular users are stored in the 'user' table
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');
const fs = require('fs');
const path = require('path');

const prisma = new PrismaClient();

// Parse command line arguments
const argv = yargs(hideBin(process.argv))
  .option('type', {
    alias: 't',
    description: 'Type of user to create (admin, agent, user, or all)',
    type: 'string',
    choices: ['admin', 'agent', 'user', 'all'],
    default: 'all',
  })
  .option('email', {
    alias: 'e',
    description: 'Email for the user',
    type: 'string',
  })
  .option('password', {
    alias: 'p',
    description: 'Password for the user',
    type: 'string',
  })
  .option('name', {
    alias: 'n',
    description: 'Name for the user',
    type: 'string',
  })
  .help()
  .alias('help', 'h').argv;

/**
 * Create an admin user
 *
 * @param {string} name - Name for the admin user
 * @param {string} email - Email for the admin user
 * @param {string} password - Password for the admin user
 * @returns {Promise<object>} - The created admin user
 */
async function createAdmin(
  name = 'Admin User',
  email = '<EMAIL>',
  password = 'Admin@123',
) {
  try {
    // Check if admin already exists
    const existingAdmin = await prisma.admin.findUnique({
      where: {
        email,
      },
    });

    if (existingAdmin) {
      console.log(`Admin user already exists with ID: ${existingAdmin.id}`);
      return existingAdmin;
    }

    // Create a new admin user
    const hashedPassword = await bcrypt.hash(password, 10);

    const newAdmin = await prisma.admin.create({
      data: {
        name,
        email,
        password: hashedPassword,
        emailVerified: true,
      },
    });

    console.log(`Created new admin user with ID: ${newAdmin.id}`);
    console.log(`Admin login: ${email} / ${password}`);
    return newAdmin;
  } catch (error) {
    console.error('Error creating admin user:', error);
    throw error;
  }
}

/**
 * Create a regular user with agent role
 *
 * Note: In this system, agents are regular users that use a special JWT token
 * signed with the agent secret key. There is no separate agent table in the database.
 *
 * @param {string} name - Name for the agent user
 * @param {string} email - Email for the agent user
 * @param {string} password - Password for the agent user
 * @returns {Promise<object>} - The created agent user
 */
async function createAgent(
  name = 'Agent User',
  email = '<EMAIL>',
  password = 'Agent@123',
) {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (existingUser) {
      console.log(`Agent user already exists with ID: ${existingUser.id}`);
      return existingUser;
    }

    // Create a new user (agent)
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        emailVerified: true,
        provider: 'credentials',
      },
    });

    console.log(`Created new agent user with ID: ${newUser.id}`);
    console.log(`Agent login: ${email} / ${password}`);

    // Note: This user will need to be authenticated with the agent JWT secret key
    return newUser;
  } catch (error) {
    console.error('Error creating agent user:', error);
    throw error;
  }
}

/**
 * Create a regular user
 *
 * @param {string} name - Name for the regular user
 * @param {string} email - Email for the regular user
 * @param {string} password - Password for the regular user
 * @returns {Promise<object>} - The created regular user
 */
async function createUser(
  name = 'Regular User',
  email = '<EMAIL>',
  password = 'User@123',
) {
  try {
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (existingUser) {
      console.log(`Regular user already exists with ID: ${existingUser.id}`);
      return existingUser;
    }

    // Create a new regular user
    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = await prisma.user.create({
      data: {
        name,
        email,
        password: hashedPassword,
        emailVerified: true,
        provider: 'credentials',
      },
    });

    console.log(`Created new regular user with ID: ${newUser.id}`);
    console.log(`User login: ${email} / ${password}`);
    return newUser;
  } catch (error) {
    console.error('Error creating regular user:', error);
    throw error;
  }
}

/**
 * Main function to create users based on command line arguments
 */
async function main() {
  try {
    const { type, email, password, name } = argv;

    // Create a credentials file to store login information
    const credentialsFile = path.join(__dirname, 'user-credentials.txt');
    const timestamp = new Date().toISOString();

    fs.writeFileSync(
      credentialsFile,
      `# User Credentials (Created on ${timestamp})\n\n`,
    );

    if (type === 'admin' || type === 'all') {
      const adminEmail = email || '<EMAIL>';
      const adminPassword = password || 'Admin@123';
      const adminName = name || 'Admin User';

      const admin = await createAdmin(adminName, adminEmail, adminPassword);

      fs.appendFileSync(credentialsFile, `## Admin User\n`);
      fs.appendFileSync(credentialsFile, `- ID: ${admin.id}\n`);
      fs.appendFileSync(credentialsFile, `- Name: ${adminName}\n`);
      fs.appendFileSync(credentialsFile, `- Email: ${adminEmail}\n`);
      fs.appendFileSync(credentialsFile, `- Password: ${adminPassword}\n\n`);
    }

    if (type === 'agent' || type === 'all') {
      const agentEmail = email || '<EMAIL>';
      const agentPassword = password || 'Agent@123';
      const agentName = name || 'Agent User';

      const agent = await createAgent(agentName, agentEmail, agentPassword);

      fs.appendFileSync(credentialsFile, `## Agent User\n`);
      fs.appendFileSync(credentialsFile, `- ID: ${agent.id}\n`);
      fs.appendFileSync(credentialsFile, `- Name: ${agentName}\n`);
      fs.appendFileSync(credentialsFile, `- Email: ${agentEmail}\n`);
      fs.appendFileSync(credentialsFile, `- Password: ${agentPassword}\n\n`);
    }

    if (type === 'user' || type === 'all') {
      const userEmail = email || '<EMAIL>';
      const userPassword = password || 'User@123';
      const userName = name || 'Regular User';

      const user = await createUser(userName, userEmail, userPassword);

      fs.appendFileSync(credentialsFile, `## Regular User\n`);
      fs.appendFileSync(credentialsFile, `- ID: ${user.id}\n`);
      fs.appendFileSync(credentialsFile, `- Name: ${userName}\n`);
      fs.appendFileSync(credentialsFile, `- Email: ${userEmail}\n`);
      fs.appendFileSync(credentialsFile, `- Password: ${userPassword}\n\n`);
    }

    console.log(`User creation completed successfully.`);
    console.log(`Credentials saved to: ${credentialsFile}`);
  } catch (error) {
    console.error('Error in main function:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the main function
main().catch((e) => {
  console.error(e);
  process.exit(1);
});
